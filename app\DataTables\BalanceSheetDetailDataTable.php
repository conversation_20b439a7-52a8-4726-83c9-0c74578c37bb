<?php

namespace App\DataTables;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class BalanceSheetDetailDataTable extends DataTable
{
    protected $asOfDate;

    public function __construct()
    {
        parent::__construct();

        $this->asOfDate = request('asOfDate')
            ? Carbon::parse(request('asOfDate'))->endOfDay()
            : Carbon::now()->endOfDay();
    }

    public function dataTable($query)
    {
        return datatables()
            ->collection($query)
            ->addColumn('account', function ($row) {
                $indent = str_repeat('&nbsp;&nbsp;', $row->level ?? 0);

                if ($row->is_type_header ?? false) {
                    return '<h4><strong>' . e($row->account_name) . '</strong></h4>';
                }
                if ($row->is_subtype_header ?? false) {
                    return '<strong>' . e($row->account_name) . '</strong>';
                }
                if ($row->is_account_header ?? false) {
                    return $indent . e($row->account_code . ' - ' . $row->account_name);
                }
                if ($row->is_account_total ?? false) {
                    return '<strong>' . $indent . 'Total ' . e($row->account_name) . '</strong>';
                }
                if ($row->is_subtype_total ?? false) {
                    return '<strong>Total ' . e($row->account_name) . '</strong>';
                }
                if ($row->is_type_total ?? false) {
                    return '<strong>Total ' . e($row->account_name) . '</strong>';
                }

                return '';
            })
            ->addColumn('date', fn($row) => $row->date ?? '')
            ->addColumn('transaction_type', fn($row) => $row->transaction_type ?? '')
            ->addColumn('num', fn($row) => $row->num ?? '')
            ->addColumn('memo', fn($row) => $row->memo ?? '')
            ->addColumn('split', fn($row) => $row->split_account ?? '')
            ->addColumn('debit', fn($row) => ($row->debit ?? null) ? number_format($row->debit, 2) : '')
            ->addColumn('credit', fn($row) => ($row->credit ?? null) ? number_format($row->credit, 2) : '')
            ->addColumn('amount', fn($row) => isset($row->amount) ? number_format($row->amount, 2) : '')
            ->addColumn('balance', fn($row) => isset($row->balance) ? number_format($row->balance, 2) : '')
            ->rawColumns(['account']);
    }

    public function query()
    {
        $entries = DB::table('journal_entry_lines')
            ->join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
            ->join('chart_of_accounts', 'journal_entry_lines.chart_of_account_id', '=', 'chart_of_accounts.id')
            ->leftJoin('chart_of_account_sub_types', 'chart_of_accounts.chart_of_account_sub_type_id', '=', 'chart_of_account_sub_types.id')
            ->leftJoin('chart_of_account_types', 'chart_of_account_sub_types.chart_of_account_type_id', '=', 'chart_of_account_types.id')
            ->where('journal_entries.company_id', company()->id)
            ->where('journal_entries.date', '<=', $this->asOfDate)
            ->where('journal_entries.status', 'draft')
            ->whereIn('chart_of_account_types.name', ['Asset', 'Liability', 'Equity'])
            ->select([
                'journal_entries.id as journal_id',
                'journal_entries.date',
                'journal_entries.voucher_type as transaction_type',
                'journal_entries.number as num',
                'journal_entries.memo as memo',
                'journal_entry_lines.debit',
                'journal_entry_lines.credit',
                DB::raw('0 as amount'), // ✅ compute later in PHP
                'chart_of_accounts.id as account_id',
                'chart_of_accounts.name as account_name',
                'chart_of_accounts.code as account_code',
                'chart_of_accounts.parent_id as parent_id',
                'chart_of_account_sub_types.name as subtype_name',
                'chart_of_account_types.name as type_name',
            ])
            ->orderBy('chart_of_account_types.name')
            ->orderBy('chart_of_account_sub_types.name')
            ->orderBy('chart_of_accounts.parent_id')
            ->orderBy('chart_of_accounts.name')
            ->orderBy('journal_entries.date')
            ->get();

        $groupedByType = $entries->groupBy('type_name');
        $report = collect();

        $totalLiabilities = 0;
        $totalEquity = 0;

        foreach ($groupedByType as $typeName => $typeEntries) {
            // === Type Header ===
            $report->push((object)[
                'is_type_header' => true,
                'account_name' => $typeName,
                'level' => 0,
            ]);

            $groupedBySubType = $typeEntries->groupBy('subtype_name');
            foreach ($groupedBySubType as $subTypeName => $subEntries) {
                // === SubType Header ===
                $report->push((object)[
                    'is_subtype_header' => true,
                    'account_name' => $subTypeName ?: 'Uncategorized',
                    'level' => 1,
                ]);

                // Recursive accounts processing
                $report = $this->processAccounts($subEntries, $report, null, 2);

                // === SubType Total ===
                $subTypeTotal = $subEntries->sum(function ($l) use ($subEntries) {
                    $accountType = $subEntries->first()->type_name;
                    return in_array($accountType, ['Asset', 'Expense'])
                        ? (($l->debit ?? 0) - ($l->credit ?? 0))
                        : (($l->credit ?? 0) - ($l->debit ?? 0));
                });

                $report->push((object)[
                    'is_subtype_total' => true,
                    'account_name' => $subTypeName ?: 'Uncategorized',
                    'balance' => $subTypeTotal,
                    'level' => 1,
                ]);
            }

            // === Type Total ===
            $typeTotal = $typeEntries->sum(function ($l) use ($typeEntries) {
                $accountType = $typeEntries->first()->type_name;
                return in_array($accountType, ['Asset', 'Expense'])
                    ? (($l->debit ?? 0) - ($l->credit ?? 0))
                    : (($l->credit ?? 0) - ($l->debit ?? 0));
            });

            $report->push((object)[
                'is_type_total' => true,
                'account_name' => $typeName,
                'balance' => $typeTotal,
                'level' => 0,
            ]);

            if ($typeName === 'Liability') {
                $totalLiabilities = $typeTotal;
            }

            if ($typeName === 'Equity') {
                $totalEquity = $typeTotal;

                // === Profit/Loss Section ===
                $plEntries = DB::table('journal_entry_lines')
                    ->join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
                    ->join('chart_of_accounts', 'journal_entry_lines.chart_of_account_id', '=', 'chart_of_accounts.id')
                    ->leftJoin('chart_of_account_sub_types', 'chart_of_accounts.chart_of_account_sub_type_id', '=', 'chart_of_account_sub_types.id')
                    ->leftJoin('chart_of_account_types', 'chart_of_account_sub_types.chart_of_account_type_id', '=', 'chart_of_account_types.id')
                    ->where('journal_entries.company_id', company()->id)
                    ->where('journal_entries.date', '<=', $this->asOfDate)
                    ->where('journal_entries.status', 'draft')
                    ->whereIn('chart_of_account_types.name', ['Income', 'Expense'])
                    ->select([
                        'journal_entries.id as journal_id',
                        'journal_entries.date',
                        'journal_entries.voucher_type as transaction_type',
                        'journal_entries.number as num',
                        'journal_entries.memo as memo',
                        'journal_entry_lines.debit',
                        'journal_entry_lines.credit',
                        DB::raw('0 as amount'), // ✅ compute later
                        'chart_of_accounts.id as account_id',
                        'chart_of_accounts.name as account_name',
                        'chart_of_accounts.code as account_code',
                        'chart_of_accounts.parent_id as parent_id',
                        'chart_of_account_sub_types.name as subtype_name',
                        'chart_of_account_types.name as type_name',
                    ])
                    ->orderBy('chart_of_account_types.name')
                    ->orderBy('chart_of_account_sub_types.name')
                    ->orderBy('chart_of_accounts.parent_id')
                    ->orderBy('chart_of_accounts.name')
                    ->orderBy('journal_entries.date')
                    ->get();

                    $netProfit = $plEntries->sum(function ($l) {
                        if ($l->type_name === 'Income') {
                            // Income increases profit
                            return ($l->credit ?? 0) - ($l->debit ?? 0);
                        } elseif ($l->type_name === 'Expense') {
                            // Expenses reduce profit
                            return -1 * (($l->debit ?? 0) - ($l->credit ?? 0));
                        }
                        return 0;
                    });


                // === Show Accumulated Profit/Loss Section ===
                $report->push((object)[
                    'is_subtype_header' => true,
                    'account_name' => "Accumulated (Loss) / Profit",
                    'level' => 1,
                ]);

                $report = $this->processAccounts($plEntries, $report, null, 2);

                $report->push((object)[
                    'is_subtype_total' => true,
                    'account_name' => "Accumulated (Loss) / Profit",
                    'balance' => $netProfit,
                    'level' => 1,
                ]);

                $totalEquity += $netProfit;

                // spacing
                $report->push((object)[
                    'account_name' => '',
                    'balance' => null,
                ]);

                // === FINAL LIABILITIES & EQUITY TOTAL ===
                $report->push((object)[
                    'is_type_total' => true,
                    'account_name' => "TOTAL LIABILITIES & EQUITY",
                    'balance' => $totalLiabilities + $totalEquity,
                    'level' => 0,
                ]);
            }
        }

        return $report;
    }

    // Recursive function to process account tree
    protected function processAccounts($accounts, $report, $parentId = null, $level = 0)
    {
        $grouped = $accounts->where('parent_id', $parentId)->groupBy('account_id');

        foreach ($grouped as $accountId => $lines) {
            $balance = 0;

            // === Account Header ===
            $report->push((object)[
                'is_account_header' => true,
                'account_name' => $lines->first()->account_name,
                'account_code' => $lines->first()->account_code,
                'level' => $level,
            ]);

            foreach ($lines as $line) {
                $accountType = $lines->first()->type_name;

                if (in_array($accountType, ['Asset', 'Expense'])) {
                    $amount = (($line->debit ?? 0) - ($line->credit ?? 0));
                    $balance += $amount;
                } else {
                    $amount = (($line->credit ?? 0) - ($line->debit ?? 0));
                    $balance += $amount;
                }

                // Find opposite (split) accounts
                $splitAccounts = DB::table('journal_entry_lines')
                    ->join('chart_of_accounts', 'journal_entry_lines.chart_of_account_id', '=', 'chart_of_accounts.id')
                    ->where('journal_entry_lines.journal_entry_id', $line->journal_id)
                    ->where('journal_entry_lines.chart_of_account_id', '!=', $line->account_id)
                    ->pluck('chart_of_accounts.name')
                    ->implode(', ');

                $report->push((object)[
                    'date' => $line->date,
                    'transaction_type' => $line->transaction_type,
                    'num' => $line->num,
                    'memo' => $line->memo,
                    'split_account' => $splitAccounts,
                    'debit' => (float) ($line->debit ?? 0),
                    'credit' => (float) ($line->credit ?? 0),
                    'amount' => (float) $amount,
                    'balance' => $balance,
                    'level' => $level,
                ]);
            }

            // === Account Total ===
            $report->push((object)[
                'is_account_total' => true,
                'account_name' => $lines->first()->account_name,
                'balance' => $balance,
                'level' => $level,
            ]);

            // === Process Children recursively ===
            $childAccounts = $accounts->where('parent_id', $accountId);
            if ($childAccounts->count() > 0) {
                $report = $this->processAccounts($accounts, $report, $accountId, $level + 1);
            }
        }

        return $report;
    }

    public function html()
    {
        return $this->builder()
            ->setTableId('balance-sheet-detail-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->parameters([
                'paging' => false,
                'searching' => false,
                'info' => false,
                'ordering' => false,
            ]);
    }

    protected function getColumns()
    {
        return [
            Column::make('account')->title('Account'),
            Column::make('date')->title('Date'),
            Column::make('transaction_type')->title('Type'),
            Column::make('num')->title('Num'),
            Column::make('memo')->title('Memo/Description'),
            Column::make('split')->title('Split Account'),
            Column::make('debit')->title('Debit')->addClass('text-right'),
            Column::make('credit')->title('Credit')->addClass('text-right'),
            Column::make('amount')->title('Amount')->addClass('text-right'),
            Column::make('balance')->title('Balance')->addClass('text-right'),
        ];
    }
}
