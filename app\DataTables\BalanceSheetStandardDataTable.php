<?php

namespace App\DataTables;

use App\Models\ChartOfAccount;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;
use Illuminate\Support\Collection;

class BalanceSheetStandardDataTable extends DataTable
{
    protected $asOfDate;

    public function __construct()
    {
        parent::__construct();

        $this->asOfDate = request('asOfDate')
            ? Carbon::parse(request('asOfDate'))->endOfDay()
            : Carbon::now()->endOfDay();
    }

    /**
     * Build DataTable response.
     */
    public function dataTable($query)
    {
        return datatables()
            ->collection($query)
            ->addColumn('account', function ($row) {
                if ($row->is_section_header ?? false) {
                    return '<strong class="section-header">' . e($row->name) . '</strong>';
                }
                if ($row->is_subtotal ?? false) {
                    return '<strong class="subtotal-label">' . e($row->name) . '</strong>';
                }
                if ($row->is_total ?? false) {
                    return '<strong class="total-label">' . e($row->name) . '</strong>';
                }

                // indent children or deeper levels
                $depth = (int) ($row->depth ?? 0);
                $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', max(0, $depth));
                return $indent . e($row->name);
            })
            ->addColumn('amount', function ($row) {
                if ($row->is_section_header ?? false) {
                    return '';
                }

                $amount = (float) ($row->amount ?? 0);

                // hide zero-non-subtotal/total rows for cleanliness
                if ($amount == 0 && !($row->is_subtotal ?? false) && !($row->is_total ?? false)) {
                    return '';
                }

                if ($row->is_subtotal ?? false) {
                    return '<strong class="subtotal-amount">' . number_format($amount, 2) . '</strong>';
                }

                if ($row->is_total ?? false) {
                    return '<strong class="total-amount">' . number_format($amount, 2) . '</strong>';
                }

                return '<span class="amount-cell">' . number_format($amount, 2) . '</span>';
            })
            ->addColumn('row_class', function ($row) {
                if ($row->is_section_header ?? false) return 'section-header-row';
                if ($row->is_subtotal ?? false) return 'subtotal-row';
                if ($row->is_total ?? false) return 'total-row';
                if ($row->depth ?? 0 > 0) return 'child-row';
                return '';
            })
            ->rawColumns(['account', 'amount']);
    }

    /**
     * Main query and report builder.
     * Returns a Collection of objects suitable for datatables collection().
     */
public function query()
{
    // Step 1: Get all accounts with balances
    $accounts = ChartOfAccount::where('chart_of_accounts.company_id', company()->id)
        ->leftJoin('chart_of_account_sub_types', 'chart_of_accounts.chart_of_account_sub_type_id', '=', 'chart_of_account_sub_types.id')
        ->leftJoin('chart_of_account_types', 'chart_of_account_sub_types.chart_of_account_type_id', '=', 'chart_of_account_types.id')
        ->leftJoin('journal_entry_lines', 'chart_of_accounts.id', '=', 'journal_entry_lines.chart_of_account_id')
        ->leftJoin('journal_entries', function($join) {
            $join->on('journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
                ->where('journal_entries.company_id', company()->id)
                ->where('journal_entries.date', '<=', $this->asOfDate)
                ->where('journal_entries.status', 'draft'); // change to 'approved' if needed
        })
        ->select([
            'chart_of_accounts.id',
            'chart_of_accounts.name',
            'chart_of_accounts.parent_id',
            'chart_of_account_sub_types.id as sub_type_id',
            'chart_of_account_sub_types.name as sub_type_name',
            'chart_of_account_types.id as type_id',
            'chart_of_account_types.name as type_name',
            DB::raw('COALESCE(SUM(journal_entry_lines.debit), 0) as total_debit'),
            DB::raw('COALESCE(SUM(journal_entry_lines.credit), 0) as total_credit'),
        ])
        ->groupBy(
            'chart_of_accounts.id',
            'chart_of_accounts.name',
            'chart_of_accounts.parent_id',
            'chart_of_account_sub_types.id',
            'chart_of_account_sub_types.name',
            'chart_of_account_types.id',
            'chart_of_account_types.name'
        )
        ->get();

    // Step 2: Calculate balances
    $accounts = $accounts->map(function($acc) {
        if ($acc->type_name === 'Asset') {
            $acc->balance = $acc->total_debit - $acc->total_credit;
        } else {
            $acc->balance = $acc->total_credit - $acc->total_debit;
        }
        return $acc;
    });

    $report = collect();

    // Step 3: Build sections (Assets, Liabilities, Equity)
    $types = $accounts->groupBy('type_name');
    $totalAssets = 0;
    $totalLiabilities = 0;
    $totalEquity = 0;

    foreach (['Asset', 'Liability', 'Equity'] as $typeName) {
        $typeAccounts = $types->get($typeName, collect());

        if ($typeAccounts->isEmpty() && $typeName !== 'Equity') {
            continue;
        }

        // Section Header
        $report->push((object)[
            'name' => strtoupper($typeName),
            'depth' => 0,
            'is_section_header' => true,
        ]);

        // SubTypes inside this Type
        $subTypes = $typeAccounts->groupBy('sub_type_name');
        foreach ($subTypes as $subTypeName => $subTypeAccounts) {
            $report->push((object)[
                'name' => $subTypeName,
                'depth' => 1,
            ]);

            // Find only parent/root accounts for this subtype
            $roots = $subTypeAccounts->filter(function($acc) use ($subTypeAccounts) {
                return !$subTypeAccounts->contains('id', $acc->parent_id);
            });

            foreach ($roots as $root) {
                $report = $report->merge($this->buildAccountTree($root, $subTypeAccounts, 2));
            }

            // SubType Total
            $subTypeTotal = $subTypeAccounts->sum('balance');
            $report->push((object)[
                'name' => "Total " . $subTypeName,
                'amount' => $subTypeTotal,
                'depth' => 1,
                'is_subtotal' => true,
            ]);
        }

        // Type Total
        $typeTotal = $typeAccounts->sum('balance');
        if ($typeName === 'Asset') {
            $totalAssets = $typeTotal;
        } elseif ($typeName === 'Liability') {
            $totalLiabilities = $typeTotal;
        } elseif ($typeName === 'Equity') {
            $totalEquity = $typeTotal;
        }

        $report->push((object)[
            'name' => "Total " . $typeName,
            'amount' => $typeTotal,
            'depth' => 0,
            'is_total' => true,
        ]);

        $report->push((object)['name' => '', 'amount' => null]); // spacing
    }

    // Step 4: Add Net Profit / Loss into Equity
    $netProfit = DB::table('journal_entry_lines')
        ->join('chart_of_accounts', 'journal_entry_lines.chart_of_account_id', '=', 'chart_of_accounts.id')
        ->join('chart_of_account_types', 'chart_of_accounts.chart_of_account_type_id', '=', 'chart_of_account_types.id')
        ->join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
        ->where('journal_entries.company_id', company()->id)
        ->where('journal_entries.date', '<=', $this->asOfDate)
        ->where('journal_entries.status', 'draft') // change to 'approved' if needed
        ->whereIn('chart_of_account_types.name', ['Income', 'Expense'])
        ->selectRaw('SUM(journal_entry_lines.credit - journal_entry_lines.debit) as net_profit')
        ->value('net_profit') ?? 0;

    $report->push((object)[
        'name' => "Accumulated (Loss) / Profit",
        'amount' => $netProfit,
        'depth' => 1,
    ]);

    $totalEquity += $netProfit;
    $report->push((object)['name' => '', 'amount' => null]); // spacing

    // Step 5: Final TOTAL LIABILITIES + EQUITY
    $report->push((object)[
        'name' => "TOTAL LIABILITIES & EQUITY",
        'amount' => $totalLiabilities + $totalEquity,
        'depth' => 0,
        'is_total' => true,
    ]);

    return $report;
}

/**
 * Recursive helper to build parent-child hierarchy
 */
private function buildAccountTree($account, $allAccounts, $depth)
{
    $rows = collect();

    // Push parent account
    $rows->push((object)[
        'name' => $account->name,
        'amount' => (float) ($account->balance ?? 0),
        'depth' => $depth,
    ]);

    // Push children recursively
    $children = $allAccounts->where('parent_id', $account->id);
    foreach ($children as $child) {
        $rows = $rows->merge($this->buildAccountTree($child, $allAccounts, $depth + 1));
    }

    return $rows;
}


    /**
     * Push a parent account and all its descendants (depth-first).
     * - $account: account object from the query (has id, name, balance, parent_id)
     * - $subAll: collection of accounts belonging to the same subtype (so children are found within)
     * - $depth: indentation depth (0 for parent)
     *
     * Returns a Collection of row objects.
     */
    private function pushAccountWithChildren($account, Collection $subAll, int $depth = 0): Collection
    {
        $rows = collect();

        // Push parent (show own balance only)
        $rows->push((object)[
            'name' => $account->name,
            'amount' => (float) ($account->balance ?? 0),
            'depth' => $depth,
        ]);

        // get direct children of this account within the same subtype
        $children = $subAll->where('parent_id', $account->id)->values();

        foreach ($children as $child) {
            // For child push with depth+1, then its children recursively
            $rows->push((object)[
                'name' => $child->name,
                'amount' => (float) ($child->balance ?? 0),
                'depth' => $depth + 1,
            ]);

            // deeper descendants
            $grandChildren = $subAll->where('parent_id', $child->id)->values();
            if ($grandChildren->count() > 0) {
                // recursively add descendants using helper
                $rows = $rows->merge($this->pushAccountDescendants($child, $subAll, $depth + 1));
            }
        }

        return $rows;
    }

    /**
     * Recursively push descendants of a node (used by pushAccountWithChildren).
     * The first child was already pushed by pushAccountWithChildren; this function
     * handles deeper levels starting from $node.
     */
    private function pushAccountDescendants($node, Collection $subAll, int $depth): Collection
    {
        $rows = collect();

        $children = $subAll->where('parent_id', $node->id)->values();

        foreach ($children as $child) {
            $rows->push((object)[
                'name' => $child->name,
                'amount' => (float) ($child->balance ?? 0),
                'depth' => $depth + 1,
            ]);

            // recursively handle deeper levels
            $rows = $rows->merge($this->pushAccountDescendants($child, $subAll, $depth + 1));
        }

        return $rows;
    }

    /**
     * HTML definition for DataTable.
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('balance-sheet-standard-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->parameters([
                'paging' => false,
                'searching' => false,
                'info' => false,
                'ordering' => false,
            ]);
    }

    protected function getColumns()
    {
        return [
            Column::make('account')->title('')->width('70%'),
            Column::make('amount')->title('TOTAL')->width('30%')->addClass('text-right'),
        ];
    }
}
